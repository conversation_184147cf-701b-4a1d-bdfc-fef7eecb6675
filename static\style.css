/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 3px solid #667eea;
}

header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Flash Messages */
.flash-messages {
    margin-bottom: 20px;
}

.alert {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    position: relative;
    animation: slideIn 0.3s ease-out;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: inherit;
}

/* Form Styles */
.upload-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 40px;
    border: 2px solid #e9ecef;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group label h3 {
    display: inline;
    font-size: 1.2rem;
    margin-right: 10px;
}

.required {
    color: #e74c3c;
    font-size: 0.9rem;
    font-weight: 500;
}

textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

small {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

/* File Upload Styles */
.file-upload-area {
    position: relative;
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.file-upload-area.dragover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: scale(1.02);
}

.upload-prompt {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #667eea;
}

.upload-prompt p {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 10px;
}

#resumes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-list {
    margin-top: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 5px;
}

.file-info {
    display: flex;
    align-items: center;
}

.file-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.file-name {
    font-weight: 500;
    color: #2c3e50;
}

.file-size {
    color: #6c757d;
    font-size: 0.85rem;
    margin-left: 10px;
}

.remove-file {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-file:hover {
    background: #c0392b;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 50px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Instructions Section */
.instructions {
    margin-bottom: 40px;
}

.instructions h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.step-number {
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h4 {
    color: #2c3e50;
    margin-bottom: 8px;
}

.step-content p {
    color: #6c757d;
    line-height: 1.5;
}

/* Features Section */
.features {
    margin-bottom: 40px;
}

.features h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-item {
    text-align: center;
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.feature-item h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.5;
}

/* Results Page Styles */
.job-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 5px solid #667eea;
}

.job-summary h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.job-info {
    display: grid;
    gap: 15px;
}

.job-detail {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.job-detail strong {
    color: #2c3e50;
    min-width: 150px;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.skill-tag {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.skill-tag.matched {
    background: #28a745;
}

.skill-tag.missing {
    background: #dc3545;
}

.experience-req {
    background: #17a2b8;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.total-count {
    background: #6f42c1;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.no-data {
    color: #6c757d;
    font-style: italic;
}

/* Results Overview */
.results-overview {
    margin-bottom: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Resume Cards */
.results-section h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.resume-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.resume-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.resume-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.resume-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.rank-badge {
    background: #6c757d;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
}

.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #8b6914;
}

.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
    color: #666;
}

.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #deb887 100%);
    color: #654321;
}

.resume-title h4 {
    flex: 1;
    color: #2c3e50;
    font-size: 1.2rem;
    margin: 0;
}

.overall-score {
    font-size: 1.5rem;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    min-width: 80px;
    text-align: center;
}

.score-high {
    background: #d4edda;
    color: #155724;
}

.score-medium {
    background: #fff3cd;
    color: #856404;
}

.score-low {
    background: #f8d7da;
    color: #721c24;
}

.resume-content {
    padding: 25px;
}

/* Score Breakdown */
.score-breakdown {
    margin-bottom: 25px;
}

.score-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 15px;
}

.score-label {
    min-width: 120px;
    font-weight: 500;
    color: #495057;
}

.score-bar {
    flex: 1;
    background: #e9ecef;
    height: 25px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    transition: width 0.8s ease-out;
}

.score-value {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.85rem;
    font-weight: 500;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Skills Analysis */
.skills-analysis {
    margin-bottom: 20px;
}

.matched-skills, .missing-skills {
    margin-bottom: 15px;
}

.matched-skills strong, .missing-skills strong {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
}

.more-skills {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* Candidate Details */
.candidate-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    margin-bottom: 8px;
}

.detail-label {
    min-width: 100px;
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #2c3e50;
}

/* Feedback Section */
.feedback-section {
    margin-bottom: 20px;
}

.feedback-section strong {
    display: block;
    margin-bottom: 10px;
    color: #2c3e50;
}

.feedback-list {
    list-style: none;
    padding: 0;
}

.feedback-list li {
    background: #f8f9fa;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

/* Resume Text Section */
.resume-text-section {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.toggle-text-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.toggle-text-btn:hover {
    background: #5a6268;
}

.resume-full-text {
    margin-top: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.resume-full-text pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
    color: #495057;
}

/* Export Section */
.export-section {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
}

.export-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.export-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    color: white;
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-top: 20px;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .instruction-grid, .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .resume-title {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .score-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .score-label {
        min-width: auto;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 5px;
    }
    
    .export-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .overview-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .upload-section {
        padding: 20px;
    }
    
    .file-upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .resume-content {
        padding: 15px;
    }
}